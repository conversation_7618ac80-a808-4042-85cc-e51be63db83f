import streamlit as st
import mysql.connector
from mysql.connector import Error
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date
from sqlalchemy import create_engine, text
import urllib.parse


# Modern UI Styling and Logo
def add_modern_styling():
    """Add modern, beautiful styling following contemporary UI principles"""
    st.markdown("""
    <style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* Global Styles */
    .stApp {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    }

    /* Hide Streamlit branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Main container styling */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
        max-width: 1200px;
    }

    /* Modern Header */
    .modern-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem 0;
        margin: -2rem -1rem 2rem -1rem;
        border-radius: 0 0 24px 24px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .modern-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    }

    .header-content {
        position: relative;
        z-index: 1;
        text-align: center;
        color: white;
    }

    .logo-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .logo-icon {
        width: 60px;
        height: 60px;
        background: rgba(255,255,255,0.2);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.3);
    }

    .logo-text {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        background: linear-gradient(45deg, #ffffff, #e0e7ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .logo-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 400;
        margin: 0.5rem 0 0 0;
    }

    /* Sidebar Styling */
    .css-1d391kg {
        background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .sidebar .sidebar-content {
        background: transparent;
    }

    /* Modern Cards */
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }

    /* Status indicators */
    .status-success {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    }

    .status-error {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    }

    /* Form styling */
    .stSelectbox > div > div {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.3s ease;
    }

    .stSelectbox > div > div:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .stTextInput > div > div > input {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.3s ease;
    }

    .stTextInput > div > div > input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Button styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    /* Dataframe styling */
    .dataframe {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }
    </style>
    """, unsafe_allow_html=True)


def create_modern_header():
    """Create a beautiful modern header with logo"""
    st.markdown("""
    <div class="modern-header">
        <div class="header-content">
            <div class="logo-container">
                <div class="logo-icon">
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4 8h6v16H4V8zm8-4h6v20h-6V4zm8 8h6v12h-6V12z" fill="white" opacity="0.9"/>
                        <circle cx="26" cy="6" r="2" fill="white" opacity="0.7"/>
                        <path d="M2 26h28" stroke="white" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
                    </svg>
                </div>
                <div>
                    <h1 class="logo-text">DataWarehouse</h1>
                    <p class="logo-subtitle">Advanced Analytics Platform</p>
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)


# Page configuration
st.set_page_config(
    page_title="DataWarehouse Analytics",
    layout="wide",
    page_icon="📊"
)

# Apply modern styling and create header
add_modern_styling()
create_modern_header()


# Database connection parameters
DB_HOST = 'localhost'
DB_PORT = 3306
DB_USER = 'root'
DB_PASSWORD = 'Floridah2024!'  # Update with your MySQL password
DB_NAME = 'gdb0041'

# Create SQLAlchemy engine for better pandas compatibility


def create_db_engine():
    """Create SQLAlchemy engine for database connections"""
    try:
        # URL encode the password to handle special characters
        encoded_password = urllib.parse.quote_plus(DB_PASSWORD)
        connection_string = f"mysql+pymysql://{DB_USER}:{encoded_password}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        engine = create_engine(connection_string)
        return engine
    except Exception as e:
        st.error(f"Failed to create database engine: {e}")
        return None

# Helper function to connect to the database (for non-pandas operations)


def get_connection():
    try:
        connection = mysql.connector.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            autocommit=True
        )
        return connection
    except mysql.connector.Error as err:
        st.error(f"Database connection failed: {err}")
        st.error(
            "Please check your MySQL credentials and ensure MySQL server is running.")
        return None

# Helper to run a query and return DataFrame


def run_query(query, params=None):
    engine = create_db_engine()
    if engine is None:
        return pd.DataFrame()

    try:
        df = pd.read_sql(query, engine, params=params)
        return df
    except Exception as e:
        st.error(f"Query Error: {e}")
        return pd.DataFrame()

# Helper to execute a command (insert/update/delete)


def run_command(query, params=None):
    conn = get_connection()
    if conn is None:
        return False, "Database connection failed"

    try:
        cursor = conn.cursor()
        cursor.execute(query, params)
        conn.commit()
        return True, None
    except Exception as e:
        return False, str(e)
    finally:
        if conn.is_connected():
            conn.close()


# Test database connection function
def test_connection():
    """Test if database connection is working"""
    engine = create_db_engine()
    if engine is None:
        return False
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        return True
    except Exception as e:
        st.error(f"Connection test failed: {e}")
        return False


# Page configuration
st.set_page_config(page_title="Data Warehouse Analytics", layout="wide")

# Database connection status
if not test_connection():
    st.markdown("""
    <div class="status-error">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
        </svg>
        Database Connection Failed
    </div>
    """, unsafe_allow_html=True)

    st.markdown("### 🔧 Troubleshooting Required")
    st.markdown("Please verify the following configuration:")

    with st.container():
        col1, col2 = st.columns([1, 1])
        with col1:
            st.markdown("**✅ Checklist:**")
            st.markdown("- MySQL server is running")
            st.markdown("- Username and password are correct")
            st.markdown("- Database 'gdb0041' exists")
            st.markdown("- User has proper permissions")

        with col2:
            st.markdown("**⚙️ Current Settings:**")
            st.code(f"""Host: {DB_HOST}
Port: {DB_PORT}
User: {DB_USER}
Database: {DB_NAME}""", language="yaml")

    st.info("Current connection settings:")
    st.code(f"""
Host: {DB_HOST}
Port: {DB_PORT}
User: {DB_USER}
Database: {DB_NAME}
    """)

    # Provide troubleshooting options
    st.subheader("Troubleshooting Options")

    with st.expander("Try Different Connection Settings"):
        st.write("You can modify the connection settings below and test:")

        with st.form("connection_test"):
            new_host = st.text_input("Host", value=DB_HOST)
            new_port = st.number_input(
                "Port", value=DB_PORT, min_value=1, max_value=65535)
            new_user = st.text_input("Username", value=DB_USER)
            new_password = st.text_input(
                "Password", type="password", value=DB_PASSWORD)
            new_database = st.text_input("Database", value=DB_NAME)

            if st.form_submit_button("Test Connection"):
                try:
                    # Test with new settings
                    encoded_password = urllib.parse.quote_plus(new_password)
                    test_connection_string = f"mysql+pymysql://{new_user}:{encoded_password}@{new_host}:{new_port}/{new_database}"
                    test_engine = create_engine(test_connection_string)

                    with test_engine.connect() as connection:
                        connection.execute(text("SELECT 1"))
                    st.success("✅ Connection successful with new settings!")
                    st.info("If this works, update the settings in the code.")
                except Exception as e:
                    st.error(f"❌ Connection failed: {e}")

    with st.expander("Common Solutions"):
        st.markdown("""
        **Common MySQL connection issues:**

        1. **Wrong Password**: Make sure the password is correct
        2. **MySQL not running**: Start MySQL service
        3. **User permissions**: Grant privileges to the user:
           ```sql
           GRANT ALL PRIVILEGES ON gdb0041.* TO 'root'@'localhost';
           FLUSH PRIVILEGES;
           ```
        4. **Database doesn't exist**: Create the database:
           ```sql
           CREATE DATABASE gdb0041;
           ```
        5. **Port blocked**: Check if port 3306 is open
        6. **Authentication plugin**: Try this if using MySQL 8.0+:
           ```sql
           ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'your_password';
           ```
        """)

    st.stop()
else:
    st.markdown("""
    <div class="status-success">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
        </svg>
        Database Connected Successfully
    </div>
    """, unsafe_allow_html=True)

# Modern Sidebar
st.sidebar.markdown("""
<div style="text-align: center; padding: 1.5rem 0; border-bottom: 1px solid #e5e7eb; margin-bottom: 1.5rem;">
    <div style="width: 48px; height: 48px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 12px; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 7h4v10H3V7zm6-3h4v16H9V4zm6 6h4v10h-4V10z" fill="white"/>
        </svg>
    </div>
    <h3 style="margin: 0; color: #1f2937; font-size: 1.25rem; font-weight: 600;">Data Warehouse</h3>
    <p style="margin: 0.5rem 0 0 0; color: #6b7280; font-size: 0.875rem;">Management Console</p>
</div>
""", unsafe_allow_html=True)

st.sidebar.markdown("### 📊 Navigation")
# Create organized menu with categories
menu_categories = {
    "🏠 Dashboard": ["Executive Dashboard", "Quick Overview"],
    "👥 Customer Management": ["View Customers", "Add Customer", "Update Customer"],
    "📦 Product Management": ["View Products", "Add Product", "Update Product"],
    "🏷️ Categories & Suppliers": ["View Categories", "Add Category", "View Suppliers", "Add Supplier"],
    "📊 Sales Analytics": ["Sales Analytics", "View Sales Data", "Add Sales Record"],
    "💰 Cost Analysis": ["Manufacturing Cost Analysis", "Freight Cost Analysis", "Gross Price Analysis"],
    "📈 Advanced Reports": ["Monthly Reports", "Product Performance", "Customer Analysis"]
}

# Create expandable menu sections
selected_menu = None
for category, items in menu_categories.items():
    with st.sidebar.expander(category, expanded=(category == "🏠 Dashboard")):
        for item in items:
            if st.button(item, key=f"btn_{item}", use_container_width=True):
                selected_menu = item

# Fallback to selectbox if no button clicked
if selected_menu is None:
    all_items = []
    for items in menu_categories.values():
        all_items.extend(items)
    selected_menu = st.sidebar.selectbox(
        "Or select from dropdown:", [""] + all_items)

menu = selected_menu if selected_menu else "Executive Dashboard"

# --- DIMENSION TABLES ---

# Customers
if menu == "View Customers":
    st.header("Customer Dimension")
    df = run_query("SELECT * FROM dim_customer ORDER BY customer_code")
    if not df.empty:
        st.dataframe(df, use_container_width=True)
        st.info(f"Total Customers: {len(df)}")
    else:
        st.warning("No customers found.")

elif menu == "Add Customer":
    st.header("Add New Customer")
    with st.form("add_customer"):
        customer_code = st.text_input("Customer Code")
        customer_name = st.text_input("Customer Name")
        platform = st.text_input("Platform")
        channel = st.selectbox(
            "Channel", ["Distributor", "Direct", "Retailer"])
        market = st.text_input("Market")
        sub_zone = st.text_input("Sub Zone")
        region = st.text_input("Region")

        submitted = st.form_submit_button("Add Customer")
        if submitted:
            ok, err = run_command(
                """INSERT INTO dim_customer (customer_code, customer_name, platform, channel, market, sub_zone, region) 
                   VALUES (%s, %s, %s, %s, %s, %s, %s)""",
                (customer_code, customer_name, platform,
                 channel, market, sub_zone, region)
            )
            if ok:
                st.success("Customer added successfully!")
            else:
                st.error(f"Error: {err}")

elif menu == "Update Customer":
    st.header("Update Customer")
    customers_df = run_query(
        "SELECT customer_code, customer_name FROM dim_customer ORDER BY customer_code")
    if not customers_df.empty:
        selected_customer = st.selectbox(
            "Select Customer", customers_df["customer_code"])
        customer_data = run_query(
            "SELECT * FROM dim_customer WHERE customer_code = %s", (selected_customer,))

        if not customer_data.empty:
            row = customer_data.iloc[0]
            with st.form("update_customer"):
                customer_name = st.text_input(
                    "Customer Name", value=row["customer_name"])
                platform = st.text_input(
                    "Platform", value=row.get("platform", ""))
                channel = st.selectbox("Channel", ["Distributor", "Direct", "Retailer"],
                                       index=["Distributor", "Direct", "Retailer"].index(row.get("channel", "Direct")))
                market = st.text_input("Market", value=row.get("market", ""))
                sub_zone = st.text_input(
                    "Sub Zone", value=row.get("sub_zone", ""))
                region = st.text_input("Region", value=row.get("region", ""))

                submitted = st.form_submit_button("Update Customer")
                if submitted:
                    ok, err = run_command(
                        """UPDATE dim_customer SET customer_name=%s, platform=%s, channel=%s, 
                           market=%s, sub_zone=%s, region=%s WHERE customer_code=%s""",
                        (customer_name, platform, channel, market,
                         sub_zone, region, selected_customer)
                    )
                    if ok:
                        st.success("Customer updated successfully!")
                    else:
                        st.error(f"Error: {err}")

# Products
elif menu == "View Products":
    st.header("Product Dimension")
    df = run_query("""
        SELECT p.*, c.category_name, s.supplier_name 
        FROM dim_product p
        LEFT JOIN categories c ON p.category_id = c.category_id
        LEFT JOIN suppliers s ON p.supplier_id = s.supplier_id
        ORDER BY p.product_code
    """)
    if not df.empty:
        st.dataframe(df, use_container_width=True)
        st.info(f"Total Products: {len(df)}")
    else:
        st.warning("No products found.")

elif menu == "Add Product":
    st.header("Add New Product")
    categories_df = run_query(
        "SELECT category_id, category_name FROM categories")
    suppliers_df = run_query(
        "SELECT supplier_id, supplier_name FROM suppliers")

    with st.form("add_product"):
        product_code = st.text_input("Product Code")
        product_name = st.text_input("Product Name")
        variant = st.text_input("Variant")

        if not categories_df.empty:
            category = st.selectbox("Category", categories_df["category_name"])
            category_id = categories_df[categories_df["category_name"]
                                        == category]["category_id"].iloc[0]
        else:
            st.warning("No categories available. Please add categories first.")
            category_id = None

        if not suppliers_df.empty:
            supplier = st.selectbox("Supplier", suppliers_df["supplier_name"])
            supplier_id = suppliers_df[suppliers_df["supplier_name"]
                                       == supplier]["supplier_id"].iloc[0]
        else:
            st.warning("No suppliers available. Please add suppliers first.")
            supplier_id = None

        submitted = st.form_submit_button("Add Product")
        if submitted and category_id and supplier_id:
            ok, err = run_command(
                """INSERT INTO dim_product (product_code, product_name, variant, category_id, supplier_id) 
                   VALUES (%s, %s, %s, %s, %s)""",
                (product_code, product_name, variant, category_id, supplier_id)
            )
            if ok:
                st.success("Product added successfully!")
            else:
                st.error(f"Error: {err}")

# Categories
elif menu == "View Categories":
    st.header("Categories")
    df = run_query("SELECT * FROM categories ORDER BY category_name")
    if not df.empty:
        st.dataframe(df, use_container_width=True)
    else:
        st.warning("No categories found.")

elif menu == "Add Category":
    st.header("Add New Category")
    with st.form("add_category"):
        category_name = st.text_input("Category Name")
        description = st.text_area("Description")

        submitted = st.form_submit_button("Add Category")
        if submitted:
            ok, err = run_command(
                "INSERT INTO categories (category_name, description) VALUES (%s, %s)",
                (category_name, description)
            )
            if ok:
                st.success("Category added successfully!")
            else:
                st.error(f"Error: {err}")

# Suppliers
elif menu == "View Suppliers":
    st.header("Suppliers")
    df = run_query("SELECT * FROM suppliers ORDER BY supplier_name")
    if not df.empty:
        st.dataframe(df, use_container_width=True)
    else:
        st.warning("No suppliers found.")

elif menu == "Add Supplier":
    st.header("Add New Supplier")
    with st.form("add_supplier"):
        supplier_name = st.text_input("Supplier Name")
        contact_person = st.text_input("Contact Person")
        email = st.text_input("Email")
        phone = st.text_input("Phone")
        address = st.text_area("Address")

        submitted = st.form_submit_button("Add Supplier")
        if submitted:
            ok, err = run_command(
                """INSERT INTO suppliers (supplier_name, contact_person, email, phone, address) 
                   VALUES (%s, %s, %s, %s, %s)""",
                (supplier_name, contact_person, email, phone, address)
            )
            if ok:
                st.success("Supplier added successfully!")
            else:
                st.error(f"Error: {err}")

# --- FACT TABLES & ANALYTICS ---

elif menu == "Sales Analytics":
    st.header("Sales Analytics Dashboard")

    # Date range selector
    col1, col2 = st.columns(2)
    with col1:
        start_date = st.date_input("Start Date", value=date(2023, 1, 1))
    with col2:
        end_date = st.date_input("End Date", value=date.today())

    # Sales trends
    sales_trend = run_query("""
        SELECT DATE(fs.date) as sale_date, 
               SUM(fs.sold_quantity) as total_quantity,
               AVG(gp.gross_price) as avg_price
        FROM fact_sales_monthly fs
        LEFT JOIN fact_gross_price gp ON fs.product_code = gp.product_code 
        WHERE fs.date BETWEEN %s AND %s
        GROUP BY DATE(fs.date)
        ORDER BY sale_date
    """, (start_date, end_date))

    if not sales_trend.empty:
        fig = px.line(sales_trend, x='sale_date', y='total_quantity',
                      title='Sales Quantity Trend')
        st.plotly_chart(fig, use_container_width=True)

        # Top products
        top_products = run_query("""
            SELECT p.product_name, SUM(fs.sold_quantity) as total_sold
            FROM fact_sales_monthly fs
            JOIN dim_product p ON fs.product_code = p.product_code
            WHERE fs.date BETWEEN %s AND %s
            GROUP BY p.product_name
            ORDER BY total_sold DESC
            LIMIT 10
        """, (start_date, end_date))

        if not top_products.empty:
            fig2 = px.bar(top_products, x='total_sold', y='product_name',
                          orientation='h', title='Top 10 Products by Sales')
            st.plotly_chart(fig2, use_container_width=True)

elif menu == "View Sales Data":
    st.header("Sales Fact Table")
    df = run_query("""
        SELECT fs.*, p.product_name, c.customer_name
        FROM fact_sales_monthly fs
        LEFT JOIN dim_product p ON fs.product_code = p.product_code
        LEFT JOIN dim_customer c ON fs.customer_code = c.customer_code
        ORDER BY fs.date DESC
        LIMIT 1000
    """)
    if not df.empty:
        st.dataframe(df, use_container_width=True)
    else:
        st.warning("No sales data found.")

elif menu == "Add Sales Record":
    st.header("Add Sales Record")
    products_df = run_query(
        "SELECT product_code, product_name FROM dim_product")
    customers_df = run_query(
        "SELECT customer_code, customer_name FROM dim_customer")

    with st.form("add_sales"):
        if not products_df.empty:
            product = st.selectbox("Product", products_df["product_name"])
            product_code = products_df[products_df["product_name"]
                                       == product]["product_code"].iloc[0]
        else:
            st.error("No products available")
            product_code = None

        if not customers_df.empty:
            customer = st.selectbox("Customer", customers_df["customer_name"])
            customer_code = customers_df[customers_df["customer_name"]
                                         == customer]["customer_code"].iloc[0]
        else:
            st.error("No customers available")
            customer_code = None

        sold_quantity = st.number_input("Sold Quantity", min_value=1)
        sale_date = st.date_input("Sale Date", value=date.today())

        submitted = st.form_submit_button("Add Sales Record")
        if submitted and product_code and customer_code:
            ok, err = run_command(
                """INSERT INTO fact_sales_monthly (product_code, customer_code, sold_quantity, date) 
                   VALUES (%s, %s, %s, %s)""",
                (product_code, customer_code, sold_quantity, sale_date)
            )
            if ok:
                st.success("Sales record added successfully!")
            else:
                st.error(f"Error: {err}")

elif menu == "Executive Dashboard":
    st.header("Executive Dashboard")

    # Key metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_customers = run_query(
            "SELECT COUNT(*) as count FROM dim_customer")
        if not total_customers.empty:
            st.metric("Total Customers", total_customers.iloc[0]['count'])

    with col2:
        total_products = run_query("SELECT COUNT(*) as count FROM dim_product")
        if not total_products.empty:
            st.metric("Total Products", total_products.iloc[0]['count'])

    with col3:
        total_sales = run_query(
            "SELECT SUM(sold_quantity) as total FROM fact_sales_monthly")
        if not total_sales.empty and total_sales.iloc[0]['total']:
            st.metric("Total Sales Quantity",
                      f"{total_sales.iloc[0]['total']:,}")

    with col4:
        avg_gross_price = run_query(
            "SELECT AVG(gross_price) as avg_price FROM fact_gross_price")
        if not avg_gross_price.empty and avg_gross_price.iloc[0]['avg_price']:
            st.metric("Avg Gross Price",
                      f"${avg_gross_price.iloc[0]['avg_price']:.2f}")

    # Sales by region
    sales_by_region = run_query("""
        SELECT c.region, SUM(fs.sold_quantity) as total_sales
        FROM fact_sales_monthly fs
        JOIN dim_customer c ON fs.customer_code = c.customer_code
        WHERE c.region IS NOT NULL
        GROUP BY c.region
        ORDER BY total_sales DESC
    """)

    if not sales_by_region.empty:
        st.subheader("Sales by Region")
        fig = px.pie(sales_by_region, values='total_sales', names='region')
        st.plotly_chart(fig, use_container_width=True)

elif menu == "Manufacturing Cost Analysis":
    st.header("Manufacturing Cost Analysis")
    df = run_query("""
        SELECT p.product_name, mc.manufacturing_cost, mc.date
        FROM fact_manufacturing_cost mc
        JOIN dim_product p ON mc.product_code = p.product_code
        ORDER BY mc.date DESC
    """)
    if not df.empty:
        st.dataframe(df, use_container_width=True)

        # Cost trend by product
        if len(df) > 1:
            fig = px.line(df, x='date', y='manufacturing_cost', color='product_name',
                          title='Manufacturing Cost Trends by Product')
            st.plotly_chart(fig, use_container_width=True)
    else:
        st.warning("No manufacturing cost data found.")

elif menu == "Freight Cost Analysis":
    st.header("Freight Cost Analysis")
    df = run_query("""
        SELECT p.product_name, fc.freight_pct, fc.other_cost, fc.date
        FROM fact_freight_cost fc
        JOIN dim_product p ON fc.product_code = p.product_code
        ORDER BY fc.date DESC
    """)
    if not df.empty:
        st.dataframe(df, use_container_width=True)
    else:
        st.warning("No freight cost data found.")

# Add more menu items for other fact tables...

elif menu == "Quick Overview":
    st.markdown("## 📊 Quick Overview")
    st.markdown(
        "Get a snapshot of your data warehouse metrics and key performance indicators.")

    try:
        # Get key metrics
        customers_count = run_query(
            "SELECT COUNT(*) as count FROM dim_customer")
        products_count = run_query("SELECT COUNT(*) as count FROM dim_product")
        total_sales = run_query(
            "SELECT SUM(sold_quantity) as total FROM fact_sales_monthly")
        avg_gross_price = run_query(
            "SELECT AVG(gross_price) as avg_price FROM fact_gross_price")

        # Create modern metric cards
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if not customers_count.empty:
                count = customers_count.iloc[0]['count']
                st.markdown(f"""
                <div class="metric-card">
                    <h3 style="margin: 0 0 0.5rem 0; color: #374151; font-size: 0.875rem; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">Total Customers</h3>
                    <p style="margin: 0; font-size: 2rem; font-weight: 700; color: #1f2937;">{count:,}</p>
                </div>
                """, unsafe_allow_html=True)

        with col2:
            if not products_count.empty:
                count = products_count.iloc[0]['count']
                st.markdown(f"""
                <div class="metric-card">
                    <h3 style="margin: 0 0 0.5rem 0; color: #374151; font-size: 0.875rem; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">Total Products</h3>
                    <p style="margin: 0; font-size: 2rem; font-weight: 700; color: #1f2937;">{count:,}</p>
                </div>
                """, unsafe_allow_html=True)

        with col3:
            if not total_sales.empty and total_sales.iloc[0]['total']:
                total = total_sales.iloc[0]['total']
                st.markdown(f"""
                <div class="metric-card">
                    <h3 style="margin: 0 0 0.5rem 0; color: #374151; font-size: 0.875rem; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">Total Sales</h3>
                    <p style="margin: 0; font-size: 2rem; font-weight: 700; color: #1f2937;">{total:,}</p>
                </div>
                """, unsafe_allow_html=True)

        with col4:
            if not avg_gross_price.empty and avg_gross_price.iloc[0]['avg_price']:
                avg_price = avg_gross_price.iloc[0]['avg_price']
                st.markdown(f"""
                <div class="metric-card">
                    <h3 style="margin: 0 0 0.5rem 0; color: #374151; font-size: 0.875rem; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">Avg Price</h3>
                    <p style="margin: 0; font-size: 2rem; font-weight: 700; color: #1f2937;">${avg_price:.2f}</p>
                </div>
                """, unsafe_allow_html=True)

        st.markdown("<br>", unsafe_allow_html=True)

        # Recent activity or trends could go here
        st.markdown("### 📈 Recent Activity")
        st.info(
            "💡 Use the navigation menu to explore detailed analytics and manage your data warehouse.")

    except Exception as e:
        st.error(f"Unable to load metrics: {e}")
        st.info("Please check your database connection and try again.")

else:
    # Default welcome screen
    st.markdown("## 🎉 Welcome to DataWarehouse Analytics")
    st.markdown(
        "Your comprehensive business intelligence platform for data-driven insights.")

    st.markdown("### 🚀 Getting Started")
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class="metric-card">
            <h4 style="margin: 0 0 1rem 0; color: #374151;">👥 Customer Management</h4>
            <p style="margin: 0; color: #6b7280; font-size: 0.875rem;">View, add, and update customer information. Track customer segments and analyze customer behavior.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="metric-card">
            <h4 style="margin: 0 0 1rem 0; color: #374151;">📊 Sales Analytics</h4>
            <p style="margin: 0; color: #6b7280; font-size: 0.875rem;">Analyze sales trends, performance metrics, and generate comprehensive reports for business insights.</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="metric-card">
            <h4 style="margin: 0 0 1rem 0; color: #374151;">💰 Cost Analysis</h4>
            <p style="margin: 0; color: #6b7280; font-size: 0.875rem;">Monitor manufacturing costs, freight expenses, and pricing strategies to optimize profitability.</p>
        </div>
        """, unsafe_allow_html=True)

    st.markdown("### 🎯 Quick Actions")
    st.markdown(
        "Select an operation from the sidebar navigation to get started with your data warehouse management.")
