import streamlit as st
import mysql.connector
from mysql.connector import Error
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date


# Logo and header styling
def add_logo_and_styling():
    st.markdown("""
    <style>
    .main-header {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 2rem;
    }
    .logo-container {
        margin-right: 1rem;
    }
    .sidebar .sidebar-content {
        background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border-left: 4px solid #4F46E5;
    }
    </style>
    """, unsafe_allow_html=True)

    # Logo SVG - save this as a separate file or embed directly
    logo_svg = """
    <svg viewBox="0 0 400 100" xmlns="http://www.w3.org/2000/svg" style="width: 300px; height: 75px;">
      <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#06B6D4;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
        </linearGradient>
      </defs>
      
      <circle cx="45" cy="50" r="35" fill="url(#grad1)" opacity="0.1"/>
      
      <g transform="translate(20, 25)">
        <ellipse cx="25" cy="15" rx="20" ry="6" fill="url(#grad1)" opacity="0.8"/>
        <ellipse cx="25" cy="25" rx="20" ry="6" fill="url(#grad1)" opacity="0.6"/>
        <ellipse cx="25" cy="35" rx="20" ry="6" fill="url(#grad1)" opacity="0.4"/>
        <rect x="5" y="15" width="2" height="20" fill="url(#grad1)"/>
        <rect x="43" y="15" width="2" height="20" fill="url(#grad1)"/>
      </g>
      
      <g transform="translate(55, 30)">
        <rect x="0" y="25" width="4" height="15" fill="url(#grad2)" rx="2"/>
        <rect x="8" y="20" width="4" height="20" fill="url(#grad2)" rx="2"/>
        <rect x="16" y="15" width="4" height="25" fill="url(#grad2)" rx="2"/>
        <rect x="24" y="10" width="4" height="30" fill="url(#grad2)" rx="2"/>
        <path d="M 2 35 Q 10 25, 18 20 Q 26 15, 34 12" stroke="url(#grad2)" stroke-width="2" fill="none" stroke-linecap="round"/>
        <circle cx="34" cy="12" r="2" fill="#06B6D4"/>
      </g>
      
      <text x="105" y="35" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1F2937">
        DataWarehouse
      </text>
      <text x="105" y="55" font-family="Arial, sans-serif" font-size="16" fill="#6B7280">
        Analytics Platform
      </text>
      <text x="105" y="75" font-family="Arial, sans-serif" font-size="12" fill="#9CA3AF" font-style="italic">
        Intelligent Business Insights
      </text>
      
      <circle cx="380" cy="20" r="3" fill="#06B6D4" opacity="0.6"/>
      <circle cx="370" cy="30" r="2" fill="#4F46E5" opacity="0.4"/>
      <circle cx="385" cy="35" r="2" fill="#7C3AED" opacity="0.5"/>
    </svg>
    """
    
    st.markdown(f"""
    <div class="main-header">
        <div class="logo-container">
            {logo_svg}
        </div>
    </div>
    """, unsafe_allow_html=True)

# Update your main app.py file structure:

# After your imports, before the sidebar, add:
add_logo_and_styling()

# Alternative: Simple text-based logo
def simple_logo():
    st.markdown("""
    <div style="text-align: center; padding: 1rem 0; border-bottom: 2px solid #4F46E5; margin-bottom: 2rem;">
        <h1 style="color: #4F46E5; margin: 0; font-size: 2.5rem;">📊 DataWarehouse</h1>
        <p style="color: #6B7280; margin: 0.5rem 0 0 0; font-style: italic;">Analytics Platform</p>
    </div>
    """, unsafe_allow_html=True)

# For sidebar logo (smaller version):
def sidebar_logo():
    st.sidebar.markdown("""
    <div style="text-align: center; padding: 1rem 0;">
        <div style="font-size: 1.5rem; color: #4F46E5; font-weight: bold;">📊 DW</div>
        <div style="font-size: 0.8rem; color: #6B7280;">Analytics</div>
    </div>
    """, unsafe_allow_html=True)

# Usage in your app:
# Add this right after st.set_page_config() and before your sidebar:

# Page configuration
st.set_page_config(
    page_title="DataWarehouse Analytics", 
    layout="wide",
    page_icon="📊"
)

# Add the logo
add_logo_and_styling()


# Database connection parameters
DB_HOST = 'localhost:3306'
DB_USER = 'root'
DB_PASSWORD = 'Floridah2024!'  # Update with your MySQL password
DB_NAME = 'gdb0041'  # Updated to match your database name

# Helper function to connect to the database
def get_connection():
    return mysql.connector.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        database=DB_NAME
    )

# Helper to run a query and return DataFrame
def run_query(query, params=None):
    conn = get_connection()
    df = pd.DataFrame()
    try:
        df = pd.read_sql(query, conn, params=params)
    except Exception as e:
        st.error(f"Error: {e}")
    finally:
        conn.close()
    return df

# Helper to execute a command (insert/update/delete)
def run_command(query, params=None):
    conn = get_connection()
    try:
        cursor = conn.cursor()
        cursor.execute(query, params)
        conn.commit()
        return True, None
    except Exception as e:
        return False, str(e)
    finally:
        conn.close()

# Page configuration
st.set_page_config(page_title="Data Warehouse Analytics", layout="wide")

# Sidebar navigation
st.sidebar.title("Data Warehouse Management")
menu = st.sidebar.selectbox(
    "Select Operation",
    [
        # Dimension Tables
        "View Customers", "Add Customer", "Update Customer",
        "View Products", "Add Product", "Update Product", 
        "View Categories", "Add Category",
        "View Suppliers", "Add Supplier",
        
        # Fact Tables & Analytics
        "Sales Analytics", "View Sales Data", "Add Sales Record",
        "Forecast Analytics", "Manufacturing Cost Analysis",
        "Freight Cost Analysis", "Gross Price Analysis",
        "Invoice Deductions Analysis",
        
        # Reports & Dashboards
        "Executive Dashboard", "Monthly Reports", "Product Performance",
        "Customer Analysis", "Cost Analysis Dashboard"
    ]
)

# --- DIMENSION TABLES ---

# Customers
if menu == "View Customers":
    st.header("Customer Dimension")
    df = run_query("SELECT * FROM dim_customer ORDER BY customer_code")
    if not df.empty:
        st.dataframe(df, use_container_width=True)
        st.info(f"Total Customers: {len(df)}")
    else:
        st.warning("No customers found.")

elif menu == "Add Customer":
    st.header("Add New Customer")
    with st.form("add_customer"):
        customer_code = st.text_input("Customer Code")
        customer_name = st.text_input("Customer Name")
        platform = st.text_input("Platform")
        channel = st.selectbox("Channel", ["Distributor", "Direct", "Retailer"])
        market = st.text_input("Market")
        sub_zone = st.text_input("Sub Zone")
        region = st.text_input("Region")
        
        submitted = st.form_submit_button("Add Customer")
        if submitted:
            ok, err = run_command(
                """INSERT INTO dim_customer (customer_code, customer_name, platform, channel, market, sub_zone, region) 
                   VALUES (%s, %s, %s, %s, %s, %s, %s)""",
                (customer_code, customer_name, platform, channel, market, sub_zone, region)
            )
            if ok:
                st.success("Customer added successfully!")
            else:
                st.error(f"Error: {err}")

elif menu == "Update Customer":
    st.header("Update Customer")
    customers_df = run_query("SELECT customer_code, customer_name FROM dim_customer ORDER BY customer_code")
    if not customers_df.empty:
        selected_customer = st.selectbox("Select Customer", customers_df["customer_code"])
        customer_data = run_query("SELECT * FROM dim_customer WHERE customer_code = %s", (selected_customer,))
        
        if not customer_data.empty:
            row = customer_data.iloc[0]
            with st.form("update_customer"):
                customer_name = st.text_input("Customer Name", value=row["customer_name"])
                platform = st.text_input("Platform", value=row.get("platform", ""))
                channel = st.selectbox("Channel", ["Distributor", "Direct", "Retailer"], 
                                     index=["Distributor", "Direct", "Retailer"].index(row.get("channel", "Direct")))
                market = st.text_input("Market", value=row.get("market", ""))
                sub_zone = st.text_input("Sub Zone", value=row.get("sub_zone", ""))
                region = st.text_input("Region", value=row.get("region", ""))
                
                submitted = st.form_submit_button("Update Customer")
                if submitted:
                    ok, err = run_command(
                        """UPDATE dim_customer SET customer_name=%s, platform=%s, channel=%s, 
                           market=%s, sub_zone=%s, region=%s WHERE customer_code=%s""",
                        (customer_name, platform, channel, market, sub_zone, region, selected_customer)
                    )
                    if ok:
                        st.success("Customer updated successfully!")
                    else:
                        st.error(f"Error: {err}")

# Products
elif menu == "View Products":
    st.header("Product Dimension")
    df = run_query("""
        SELECT p.*, c.category_name, s.supplier_name 
        FROM dim_product p
        LEFT JOIN categories c ON p.category_id = c.category_id
        LEFT JOIN suppliers s ON p.supplier_id = s.supplier_id
        ORDER BY p.product_code
    """)
    if not df.empty:
        st.dataframe(df, use_container_width=True)
        st.info(f"Total Products: {len(df)}")
    else:
        st.warning("No products found.")

elif menu == "Add Product":
    st.header("Add New Product")
    categories_df = run_query("SELECT category_id, category_name FROM categories")
    suppliers_df = run_query("SELECT supplier_id, supplier_name FROM suppliers")
    
    with st.form("add_product"):
        product_code = st.text_input("Product Code")
        product_name = st.text_input("Product Name")
        variant = st.text_input("Variant")
        
        if not categories_df.empty:
            category = st.selectbox("Category", categories_df["category_name"])
            category_id = categories_df[categories_df["category_name"] == category]["category_id"].iloc[0]
        else:
            st.warning("No categories available. Please add categories first.")
            category_id = None
            
        if not suppliers_df.empty:
            supplier = st.selectbox("Supplier", suppliers_df["supplier_name"])
            supplier_id = suppliers_df[suppliers_df["supplier_name"] == supplier]["supplier_id"].iloc[0]
        else:
            st.warning("No suppliers available. Please add suppliers first.")
            supplier_id = None
        
        submitted = st.form_submit_button("Add Product")
        if submitted and category_id and supplier_id:
            ok, err = run_command(
                """INSERT INTO dim_product (product_code, product_name, variant, category_id, supplier_id) 
                   VALUES (%s, %s, %s, %s, %s)""",
                (product_code, product_name, variant, category_id, supplier_id)
            )
            if ok:
                st.success("Product added successfully!")
            else:
                st.error(f"Error: {err}")

# Categories
elif menu == "View Categories":
    st.header("Categories")
    df = run_query("SELECT * FROM categories ORDER BY category_name")
    if not df.empty:
        st.dataframe(df, use_container_width=True)
    else:
        st.warning("No categories found.")

elif menu == "Add Category":
    st.header("Add New Category")
    with st.form("add_category"):
        category_name = st.text_input("Category Name")
        description = st.text_area("Description")
        
        submitted = st.form_submit_button("Add Category")
        if submitted:
            ok, err = run_command(
                "INSERT INTO categories (category_name, description) VALUES (%s, %s)",
                (category_name, description)
            )
            if ok:
                st.success("Category added successfully!")
            else:
                st.error(f"Error: {err}")

# Suppliers
elif menu == "View Suppliers":
    st.header("Suppliers")
    df = run_query("SELECT * FROM suppliers ORDER BY supplier_name")
    if not df.empty:
        st.dataframe(df, use_container_width=True)
    else:
        st.warning("No suppliers found.")

elif menu == "Add Supplier":
    st.header("Add New Supplier")
    with st.form("add_supplier"):
        supplier_name = st.text_input("Supplier Name")
        contact_person = st.text_input("Contact Person")
        email = st.text_input("Email")
        phone = st.text_input("Phone")
        address = st.text_area("Address")
        
        submitted = st.form_submit_button("Add Supplier")
        if submitted:
            ok, err = run_command(
                """INSERT INTO suppliers (supplier_name, contact_person, email, phone, address) 
                   VALUES (%s, %s, %s, %s, %s)""",
                (supplier_name, contact_person, email, phone, address)
            )
            if ok:
                st.success("Supplier added successfully!")
            else:
                st.error(f"Error: {err}")

# --- FACT TABLES & ANALYTICS ---

elif menu == "Sales Analytics":
    st.header("Sales Analytics Dashboard")
    
    # Date range selector
    col1, col2 = st.columns(2)
    with col1:
        start_date = st.date_input("Start Date", value=date(2023, 1, 1))
    with col2:
        end_date = st.date_input("End Date", value=date.today())
    
    # Sales trends
    sales_trend = run_query("""
        SELECT DATE(fs.date) as sale_date, 
               SUM(fs.sold_quantity) as total_quantity,
               AVG(gp.gross_price) as avg_price
        FROM fact_sales_monthly fs
        LEFT JOIN fact_gross_price gp ON fs.product_code = gp.product_code 
        WHERE fs.date BETWEEN %s AND %s
        GROUP BY DATE(fs.date)
        ORDER BY sale_date
    """, (start_date, end_date))
    
    if not sales_trend.empty:
        fig = px.line(sales_trend, x='sale_date', y='total_quantity', 
                     title='Sales Quantity Trend')
        st.plotly_chart(fig, use_container_width=True)
        
        # Top products
        top_products = run_query("""
            SELECT p.product_name, SUM(fs.sold_quantity) as total_sold
            FROM fact_sales_monthly fs
            JOIN dim_product p ON fs.product_code = p.product_code
            WHERE fs.date BETWEEN %s AND %s
            GROUP BY p.product_name
            ORDER BY total_sold DESC
            LIMIT 10
        """, (start_date, end_date))
        
        if not top_products.empty:
            fig2 = px.bar(top_products, x='total_sold', y='product_name', 
                         orientation='h', title='Top 10 Products by Sales')
            st.plotly_chart(fig2, use_container_width=True)

elif menu == "View Sales Data":
    st.header("Sales Fact Table")
    df = run_query("""
        SELECT fs.*, p.product_name, c.customer_name
        FROM fact_sales_monthly fs
        LEFT JOIN dim_product p ON fs.product_code = p.product_code
        LEFT JOIN dim_customer c ON fs.customer_code = c.customer_code
        ORDER BY fs.date DESC
        LIMIT 1000
    """)
    if not df.empty:
        st.dataframe(df, use_container_width=True)
    else:
        st.warning("No sales data found.")

elif menu == "Add Sales Record":
    st.header("Add Sales Record")
    products_df = run_query("SELECT product_code, product_name FROM dim_product")
    customers_df = run_query("SELECT customer_code, customer_name FROM dim_customer")
    
    with st.form("add_sales"):
        if not products_df.empty:
            product = st.selectbox("Product", products_df["product_name"])
            product_code = products_df[products_df["product_name"] == product]["product_code"].iloc[0]
        else:
            st.error("No products available")
            product_code = None
            
        if not customers_df.empty:
            customer = st.selectbox("Customer", customers_df["customer_name"])
            customer_code = customers_df[customers_df["customer_name"] == customer]["customer_code"].iloc[0]
        else:
            st.error("No customers available")
            customer_code = None
            
        sold_quantity = st.number_input("Sold Quantity", min_value=1)
        sale_date = st.date_input("Sale Date", value=date.today())
        
        submitted = st.form_submit_button("Add Sales Record")
        if submitted and product_code and customer_code:
            ok, err = run_command(
                """INSERT INTO fact_sales_monthly (product_code, customer_code, sold_quantity, date) 
                   VALUES (%s, %s, %s, %s)""",
                (product_code, customer_code, sold_quantity, sale_date)
            )
            if ok:
                st.success("Sales record added successfully!")
            else:
                st.error(f"Error: {err}")

elif menu == "Executive Dashboard":
    st.header("Executive Dashboard")
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_customers = run_query("SELECT COUNT(*) as count FROM dim_customer")
        if not total_customers.empty:
            st.metric("Total Customers", total_customers.iloc[0]['count'])
    
    with col2:
        total_products = run_query("SELECT COUNT(*) as count FROM dim_product")
        if not total_products.empty:
            st.metric("Total Products", total_products.iloc[0]['count'])
    
    with col3:
        total_sales = run_query("SELECT SUM(sold_quantity) as total FROM fact_sales_monthly")
        if not total_sales.empty and total_sales.iloc[0]['total']:
            st.metric("Total Sales Quantity", f"{total_sales.iloc[0]['total']:,}")
    
    with col4:
        avg_gross_price = run_query("SELECT AVG(gross_price) as avg_price FROM fact_gross_price")
        if not avg_gross_price.empty and avg_gross_price.iloc[0]['avg_price']:
            st.metric("Avg Gross Price", f"${avg_gross_price.iloc[0]['avg_price']:.2f}")
    
    # Sales by region
    sales_by_region = run_query("""
        SELECT c.region, SUM(fs.sold_quantity) as total_sales
        FROM fact_sales_monthly fs
        JOIN dim_customer c ON fs.customer_code = c.customer_code
        WHERE c.region IS NOT NULL
        GROUP BY c.region
        ORDER BY total_sales DESC
    """)
    
    if not sales_by_region.empty:
        st.subheader("Sales by Region")
        fig = px.pie(sales_by_region, values='total_sales', names='region')
        st.plotly_chart(fig, use_container_width=True)

elif menu == "Manufacturing Cost Analysis":
    st.header("Manufacturing Cost Analysis")
    df = run_query("""
        SELECT p.product_name, mc.manufacturing_cost, mc.date
        FROM fact_manufacturing_cost mc
        JOIN dim_product p ON mc.product_code = p.product_code
        ORDER BY mc.date DESC
    """)
    if not df.empty:
        st.dataframe(df, use_container_width=True)
        
        # Cost trend by product
        if len(df) > 1:
            fig = px.line(df, x='date', y='manufacturing_cost', color='product_name',
                         title='Manufacturing Cost Trends by Product')
            st.plotly_chart(fig, use_container_width=True)
    else:
        st.warning("No manufacturing cost data found.")

elif menu == "Freight Cost Analysis":
    st.header("Freight Cost Analysis")
    df = run_query("""
        SELECT p.product_name, fc.freight_pct, fc.other_cost, fc.date
        FROM fact_freight_cost fc
        JOIN dim_product p ON fc.product_code = p.product_code
        ORDER BY fc.date DESC
    """)
    if not df.empty:
        st.dataframe(df, use_container_width=True)
    else:
        st.warning("No freight cost data found.")

# Add more menu items for other fact tables...

else:
    st.header("Welcome to Data Warehouse Analytics")
    st.write("Select an operation from the sidebar to get started.")
    
    # Quick stats
    st.subheader("Quick Overview")
    try:
        customers_count = run_query("SELECT COUNT(*) as count FROM dim_customer")
        products_count = run_query("SELECT COUNT(*) as count FROM dim_product")
        
        col1, col2 = st.columns(2)
        with col1:
            if not customers_count.empty:
                st.info(f"Total Customers: {customers_count.iloc[0]['count']}")
        with col2:
            if not products_count.empty:
                st.info(f"Total Products: {products_count.iloc[0]['count']}")
                
    except Exception as e:
        st.warning("Unable to load quick stats. Please check your database connection.")